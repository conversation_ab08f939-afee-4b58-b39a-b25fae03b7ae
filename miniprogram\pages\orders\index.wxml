<!--pages/orders/index.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:elif="{{isEmpty}}">
    <view class="empty-icon">📋</view>
    <text class="empty-text">暂无订单记录</text>
    <button class="empty-btn" bindtap="onContactService">联系客服</button>
  </view>

  <!-- 订单列表 -->
  <view class="orders-list" wx:else>
    <view 
      class="order-item" 
      wx:for="{{orders}}" 
      wx:key="_id"
      bindtap="onViewOrderDetail"
      data-id="{{item._id}}"
    >
      <!-- 订单头部 -->
      <view class="order-header">
        <view class="order-info">
          <text class="order-number">订单号: {{item.orderNumber}}</text>
          <text class="order-time">{{item.createTimeStr}}</text>
        </view>
        <view class="order-status {{item.statusClass}}">
          {{item.statusText}}
        </view>
      </view>

      <!-- 产品信息 -->
      <view class="product-info">
        <view class="product-icon">
          {{item.authType === 'personal' ? '🔒' : '🌐'}}
        </view>
        <view class="product-details">
          <text class="product-name">{{item.productName}}</text>
          <text class="product-desc">
            {{item.authType === 'personal' ? '个人版 (绑定机器码)' : '通用版 (不限设备)'}}
          </text>
          <text class="product-duration" wx:if="{{item.duration}}">
            授权时长: {{item.duration}}天
          </text>
        </view>
        <view class="product-price">
          <text class="price">¥{{item.price}}</text>
        </view>
      </view>

      <!-- 授权码信息 (已完成的订单) -->
      <view class="auth-code-section" wx:if="{{item.status === 'completed' && item.authCode}}">
        <view class="auth-code-header">
          <text class="auth-code-label">授权码</text>
          <text class="expire-time" wx:if="{{item.expireTime}}">
            到期时间: {{item.expireTimeStr}}
          </text>
        </view>
        <view class="auth-code-content">
          <text class="auth-code-text">{{item.authCode}}</text>
          <button 
            class="copy-btn" 
            bindtap="onCopyAuthCode" 
            data-code="{{item.authCode}}"
            catchtap=""
          >
            复制
          </button>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="order-actions">
        <button 
          class="action-btn secondary" 
          bindtap="onContactService"
          catchtap=""
        >
          联系客服
        </button>
        
        <button 
          class="action-btn primary" 
          wx:if="{{item.status === 'pending'}}"
          bindtap="onContinuePay"
          data-id="{{item._id}}"
          catchtap=""
        >
          继续支付
        </button>
        
        <button 
          class="action-btn primary" 
          wx:elif="{{item.status === 'completed' && item.authCode}}"
          bindtap="onCopyAuthCode"
          data-code="{{item.authCode}}"
          catchtap=""
        >
          复制授权码
        </button>
      </view>
    </view>
  </view>
</view>
