const cloud = require("wx-server-sdk");
const crypto = require('crypto');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
});

const db = cloud.database();

// 授权码生成器类
class AuthCodeGenerator {
  constructor() {
    this.complexSalt1 = "COMPLEX_SALT_2024";
    this.xorKey = "XIAOFU_COMPLEX_KEY_2024_SECURE";
  }

  // 生成复杂哈希
  generateComplexHash(inputStr) {
    // 第一重哈希
    const hash1 = crypto.createHash('sha256').update(inputStr).digest('base64');
    
    // 第二重哈希
    const hash2Input = hash1 + this.complexSalt1;
    const hash2 = crypto.createHash('sha256').update(hash2Input).digest('base64');
    
    // 第三重哈希
    const hash3Input = hash2 + inputStr.length.toString();
    const hash3 = crypto.createHash('sha256').update(hash3Input).digest('base64');
    
    return hash1 + hash2 + hash3;
  }

  // XOR加密
  xorEncrypt(data) {
    const dataBytes = Buffer.from(data, 'utf8');
    const keyBytes = Buffer.from(this.xorKey, 'utf8');
    const encrypted = Buffer.alloc(dataBytes.length);
    
    for (let i = 0; i < dataBytes.length; i++) {
      encrypted[i] = dataBytes[i] ^ keyBytes[i % keyBytes.length];
    }
    
    return encrypted;
  }

  // 复杂加密授权码
  encryptComplexAuthCode(authData) {
    // XOR加密
    const encrypted = this.xorEncrypt(authData);
    
    // 第一层Base64编码
    const layer1 = encrypted.toString('base64');
    
    // 第二层Base64编码
    const layer2 = Buffer.from(layer1, 'utf8').toString('base64');
    
    return layer2;
  }

  // 转换为.NET DateTime.ToBinary()兼容格式
  toNetDateTimeBinary(date) {
    // .NET DateTime的起始时间是公元1年1月1日
    const netEpoch = new Date(1, 0, 1);
    const ticks = Math.floor((date.getTime() - netEpoch.getTime()) * 10000);
    return ticks.toString();
  }

  // 创建个人版授权码
  createPersonalAuthCode(machineCode, expireTime) {
    try {
      // 生成复杂机器码哈希（取前32位）
      const machineHashFull = this.generateComplexHash(machineCode);
      const machineHash = machineHashFull.substring(0, 32);
      
      // 转换过期时间为.NET DateTime.ToBinary()兼容格式
      const timestampStr = this.toNetDateTimeBinary(expireTime);
      
      // 生成校验码（取前16位）
      const checksumInput = machineHash + timestampStr + "XIAOFU_CHECK";
      const checksumFull = this.generateComplexHash(checksumInput);
      const checksum = checksumFull.substring(0, 16);
      
      // 生成签名（取前20位）
      const signatureInput = machineHash + timestampStr + checksum + "XIAOFU_SIGN";
      const signatureFull = this.generateComplexHash(signatureInput);
      const signature = signatureFull.substring(0, 20);
      
      // 组合授权码数据
      const authData = `${machineHash}|${timestampStr}|${checksum}|${signature}`;
      
      // 复杂加密
      const encryptedAuthCode = this.encryptComplexAuthCode(authData);
      
      return encryptedAuthCode;
    } catch (error) {
      throw new Error(`创建个人版授权码失败: ${error.message}`);
    }
  }

  // 创建通用版授权码
  createUniversalAuthCode(expireTime) {
    try {
      // 转换过期时间为.NET DateTime.ToBinary()兼容格式
      const timestampStr = this.toNetDateTimeBinary(expireTime);
      
      // 生成校验码（取前16位）
      const checksumInput = "UNIVERSAL" + timestampStr + "XIAOFU_UNIVERSAL_CHECK";
      const checksumFull = this.generateComplexHash(checksumInput);
      const checksum = checksumFull.substring(0, 16);
      
      // 生成签名（取前20位）
      const signatureInput = "UNIVERSAL" + timestampStr + checksum + "XIAOFU_UNIVERSAL_SIGN";
      const signatureFull = this.generateComplexHash(signatureInput);
      const signature = signatureFull.substring(0, 20);
      
      // 组合授权码数据
      const authData = `UNIVERSAL|${timestampStr}|${checksum}|${signature}`;
      
      // 复杂加密
      const encryptedAuthCode = this.encryptComplexAuthCode(authData);
      
      // 添加通用版前缀
      return "UNIVERSAL_" + encryptedAuthCode;
    } catch (error) {
      throw new Error(`创建通用版授权码失败: ${error.message}`);
    }
  }
}

// 生成订单号
function generateOrderNumber() {
  const now = new Date();
  const timestamp = now.getTime().toString();
  const random = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `XF${timestamp}${random}`;
}

// 创建订单
async function createOrder(event) {
  try {
    const wxContext = cloud.getWXContext();
    const { orderData } = event;
    
    const orderNumber = generateOrderNumber();
    const now = new Date();
    
    const order = {
      orderNumber: orderNumber,
      openId: wxContext.OPENID,
      authType: orderData.authType,
      duration: orderData.duration,
      machineCode: orderData.machineCode || '',
      price: orderData.price,
      productName: orderData.productName,
      status: 'pending', // pending, paid, completed, cancelled, expired
      createTime: now.getTime(),
      updateTime: now.getTime(),
      expireTime: null,
      authCode: null
    };
    
    const result = await db.collection('orders').add({
      data: order
    });
    
    return {
      success: true,
      orderId: result._id,
      orderNumber: orderNumber
    };
  } catch (error) {
    console.error('创建订单失败:', error);
    return {
      success: false,
      message: '创建订单失败'
    };
  }
}

// 生成授权码
async function generateAuthCode(event) {
  try {
    const { orderId } = event;
    
    // 获取订单信息
    const orderResult = await db.collection('orders').doc(orderId).get();
    if (!orderResult.data) {
      return {
        success: false,
        message: '订单不存在'
      };
    }
    
    const order = orderResult.data;
    
    // 检查订单状态
    if (order.status !== 'paid') {
      return {
        success: false,
        message: '订单未支付'
      };
    }
    
    // 计算过期时间
    const expireTime = new Date(Date.now() + order.duration * 24 * 60 * 60 * 1000);
    
    // 生成授权码
    const generator = new AuthCodeGenerator();
    let authCode;
    
    if (order.authType === 'personal') {
      authCode = generator.createPersonalAuthCode(order.machineCode, expireTime);
    } else {
      authCode = generator.createUniversalAuthCode(expireTime);
    }
    
    // 更新订单
    await db.collection('orders').doc(orderId).update({
      data: {
        authCode: authCode,
        expireTime: expireTime.getTime(),
        status: 'completed',
        updateTime: Date.now()
      }
    });
    
    return {
      success: true,
      authCode: authCode,
      expireTime: expireTime.getTime()
    };
  } catch (error) {
    console.error('生成授权码失败:', error);
    return {
      success: false,
      message: '生成授权码失败'
    };
  }
}

// 获取用户订单列表
async function getOrders(event) {
  try {
    const wxContext = cloud.getWXContext();
    
    const result = await db.collection('orders')
      .where({
        openId: wxContext.OPENID
      })
      .orderBy('createTime', 'desc')
      .get();
    
    return {
      success: true,
      orders: result.data
    };
  } catch (error) {
    console.error('获取订单列表失败:', error);
    return {
      success: false,
      message: '获取订单列表失败'
    };
  }
}

// 获取用户统计数据
async function getUserStatistics(event) {
  try {
    const wxContext = cloud.getWXContext();
    
    const ordersResult = await db.collection('orders')
      .where({
        openId: wxContext.OPENID
      })
      .get();
    
    const orders = ordersResult.data;
    const totalOrders = orders.length;
    const activeAuthCodes = orders.filter(order => 
      order.status === 'completed' && 
      order.expireTime && 
      order.expireTime > Date.now()
    ).length;
    const totalSpent = orders
      .filter(order => order.status === 'completed')
      .reduce((sum, order) => sum + order.price, 0);
    
    return {
      success: true,
      statistics: {
        totalOrders,
        activeAuthCodes,
        totalSpent: totalSpent.toFixed(2)
      }
    };
  } catch (error) {
    console.error('获取统计数据失败:', error);
    return {
      success: false,
      statistics: {
        totalOrders: 0,
        activeAuthCodes: 0,
        totalSpent: '0.00'
      }
    };
  }
}

// 获取订单信息
async function getOrderInfo(event) {
  try {
    const { orderId } = event;
    const wxContext = cloud.getWXContext();

    const result = await db.collection('orders').doc(orderId).get();
    if (!result.data) {
      return {
        success: false,
        message: '订单不存在'
      };
    }

    const order = result.data;

    // 验证订单所有者
    if (order.openId !== wxContext.OPENID) {
      return {
        success: false,
        message: '无权访问此订单'
      };
    }

    return {
      success: true,
      orderInfo: order
    };
  } catch (error) {
    console.error('获取订单信息失败:', error);
    return {
      success: false,
      message: '获取订单信息失败'
    };
  }
}

// 更新订单状态
async function updateOrderStatus(event) {
  try {
    const { orderId, status } = event;
    const wxContext = cloud.getWXContext();

    // 先验证订单所有者
    const orderResult = await db.collection('orders').doc(orderId).get();
    if (!orderResult.data || orderResult.data.openId !== wxContext.OPENID) {
      return {
        success: false,
        message: '无权操作此订单'
      };
    }

    await db.collection('orders').doc(orderId).update({
      data: {
        status: status,
        updateTime: Date.now()
      }
    });

    return {
      success: true
    };
  } catch (error) {
    console.error('更新订单状态失败:', error);
    return {
      success: false,
      message: '更新订单状态失败'
    };
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  switch (event.type) {
    case 'createOrder':
      return await createOrder(event);
    case 'generateAuthCode':
      return await generateAuthCode(event);
    case 'getOrders':
      return await getOrders(event);
    case 'getUserStatistics':
      return await getUserStatistics(event);
    case 'getOrderInfo':
      return await getOrderInfo(event);
    case 'updateOrderStatus':
      return await updateOrderStatus(event);
    default:
      return {
        success: false,
        message: '未知的操作类型'
      };
  }
};
