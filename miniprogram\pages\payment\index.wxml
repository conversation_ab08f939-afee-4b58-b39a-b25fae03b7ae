<!--pages/payment/index.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 支付页面内容 -->
  <view class="payment-content" wx:else>
    <!-- 订单信息 -->
    <view class="order-section">
      <view class="section-title">订单信息</view>
      <view class="order-info">
        <view class="order-item">
          <text class="label">商品名称</text>
          <text class="value">{{orderInfo.productName}}</text>
        </view>
        <view class="order-item">
          <text class="label">订单号</text>
          <text class="value">{{orderInfo.orderNumber}}</text>
        </view>
        <view class="order-item">
          <text class="label">授权类型</text>
          <text class="value">{{orderInfo.authType === 'personal' ? '个人版' : '通用版'}}</text>
        </view>
        <view class="order-item">
          <text class="label">授权时长</text>
          <text class="value">{{orderInfo.duration}}天</text>
        </view>
        <view class="order-item total">
          <text class="label">支付金额</text>
          <text class="value price">¥{{orderInfo.price}}</text>
        </view>
      </view>
    </view>

    <!-- 支付方式 -->
    <view class="payment-section">
      <view class="section-title">支付方式</view>
      <view class="payment-methods">
        <view 
          class="payment-method {{selectedPaymentMethod === item.id ? 'selected' : ''}}"
          wx:for="{{paymentMethods}}" 
          wx:key="id"
          bindtap="onSelectPaymentMethod"
          data-id="{{item.id}}"
        >
          <view class="method-icon">{{item.icon}}</view>
          <view class="method-info">
            <text class="method-name">{{item.name}}</text>
            <text class="method-desc">{{item.description}}</text>
          </view>
          <view class="method-check">
            <view class="check-icon {{selectedPaymentMethod === item.id ? 'checked' : ''}}"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 支付协议 -->
    <view class="agreement-section">
      <text class="agreement-text">
        点击"立即支付"即表示您同意并接受相关服务条款，授权码一经生成不可退款。
      </text>
    </view>

    <!-- 支付按钮 -->
    <view class="payment-actions">
      <button class="pay-btn" bindtap="onPay">
        立即支付 ¥{{orderInfo.price}}
      </button>
      <view class="action-links">
        <text class="link" bindtap="onCancel">取消支付</text>
        <text class="link" bindtap="onContactService">联系客服</text>
      </view>
    </view>
  </view>
</view>
