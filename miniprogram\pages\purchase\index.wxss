/* pages/purchase/index.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: #666;
}

.section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tip-btn {
  font-size: 24rpx;
  color: #1AAD19;
  font-weight: normal;
}

/* 授权类型样式 */
.auth-types {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.auth-type-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  transition: all 0.3s;
}

.auth-type-item.selected {
  border-color: #1AAD19;
  background-color: #f0f9f0;
}

.auth-type-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.auth-type-content {
  flex: 1;
}

.auth-type-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.auth-type-desc {
  font-size: 26rpx;
  color: #666;
}

.auth-type-check {
  width: 40rpx;
  height: 40rpx;
}

.check-icon {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  position: relative;
}

.check-icon.checked {
  border-color: #1AAD19;
  background-color: #1AAD19;
}

.check-icon.checked::after {
  content: '';
  position: absolute;
  left: 12rpx;
  top: 6rpx;
  width: 12rpx;
  height: 20rpx;
  border: 3rpx solid white;
  border-top: none;
  border-left: none;
  transform: rotate(45deg);
}

/* 机器码输入样式 */
.machine-code-input {
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 20rpx;
}

.machine-code-textarea {
  width: 100%;
  min-height: 120rpx;
  font-size: 28rpx;
  line-height: 1.5;
}

/* 时长选择样式 */
.durations {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.duration-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  position: relative;
  transition: all 0.3s;
}

.duration-item.selected {
  border-color: #1AAD19;
  background-color: #f0f9f0;
}

.duration-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.duration-price {
  text-align: center;
}

.current-price {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff4444;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  margin-left: 10rpx;
}

.duration-check {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 30rpx;
  height: 30rpx;
}

.duration-check .check-icon {
  width: 30rpx;
  height: 30rpx;
}

.duration-check .check-icon.checked::after {
  left: 9rpx;
  top: 3rpx;
  width: 8rpx;
  height: 16rpx;
}

/* 购买区域样式 */
.purchase-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.price-info {
  text-align: center;
  margin-bottom: 30rpx;
}

.total-price {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff4444;
}

.price-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-top: 10rpx;
}

.purchase-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #1AAD19, #0d8c13);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.purchase-btn:active {
  opacity: 0.8;
}

/* 购买须知 */
.notice-section {
  text-align: center;
  padding: 20rpx;
}

.notice-link {
  font-size: 26rpx;
  color: #1AAD19;
  text-decoration: underline;
}

/* 弹窗样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  width: 80%;
  max-width: 600rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
}

.modal-body {
  padding: 30rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.tip-number {
  font-size: 28rpx;
  color: #1AAD19;
  font-weight: bold;
  margin-right: 10rpx;
  min-width: 30rpx;
}

.tip-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.tip-note {
  font-size: 24rpx;
  color: #ff4444;
  background-color: #fff5f5;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-top: 20rpx;
  line-height: 1.5;
}
