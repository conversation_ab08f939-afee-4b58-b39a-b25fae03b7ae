// pages/paymentSuccess/index.js
Page({
  data: {
    orderId: '',
    authCode: '',
    orderInfo: null,
    showAuthCode: false
  },

  onLoad(options) {
    if (options.orderId && options.authCode) {
      this.setData({
        orderId: options.orderId,
        authCode: options.authCode
      });
      this.loadOrderInfo();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/orders/index'
        });
      }, 1500);
    }
  },

  // 加载订单信息
  loadOrderInfo() {
    wx.cloud.callFunction({
      name: 'authFunctions',
      data: {
        type: 'getOrderInfo',
        orderId: this.data.orderId
      }
    }).then(res => {
      if (res.result.success) {
        this.setData({
          orderInfo: res.result.orderInfo
        });
      }
    }).catch(err => {
      console.error('加载订单信息失败:', err);
    });
  },

  // 显示授权码
  onShowAuthCode() {
    this.setData({
      showAuthCode: true
    });
  },

  // 复制授权码
  onCopyAuthCode() {
    wx.setClipboardData({
      data: this.data.authCode,
      success: () => {
        wx.showToast({
          title: '授权码已复制',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 查看订单
  onViewOrders() {
    wx.switchTab({
      url: '/pages/orders/index'
    });
  },

  // 继续购买
  onContinueShopping() {
    wx.switchTab({
      url: '/pages/purchase/index'
    });
  },

  // 联系客服
  onContactService() {
    wx.showActionSheet({
      itemList: ['复制客服QQ', '复制QQ群号'],
      success: (res) => {
        if (res.tapIndex === 0) {
          wx.setClipboardData({
            data: '1922759464',
            success: () => {
              wx.showToast({
                title: '客服QQ已复制',
                icon: 'success'
              });
            }
          });
        } else if (res.tapIndex === 1) {
          wx.setClipboardData({
            data: '967758553',
            success: () => {
              wx.showToast({
                title: 'QQ群号已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  },

  // 查看使用说明
  onViewInstructions() {
    wx.showModal({
      title: '使用说明',
      content: '1. 复制上方生成的授权码\n2. 打开XIAOFU工具箱软件\n3. 在授权界面粘贴授权码\n4. 点击激活即可开始使用\n\n注意：授权码请妥善保管，丢失不补',
      showCancel: false
    });
  }
});
