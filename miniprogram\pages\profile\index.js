// pages/profile/index.js
Page({
  data: {
    userInfo: null,
    hasUserInfo: false,
    statistics: {
      totalOrders: 0,
      activeAuthCodes: 0,
      totalSpent: 0
    }
  },

  onLoad() {
    this.getUserInfo();
    this.loadStatistics();
  },

  onShow() {
    this.loadStatistics();
  },

  // 获取用户信息
  getUserInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      });
    }
  },

  // 用户登录
  onLogin() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        });
        wx.setStorageSync('userInfo', res.userInfo);
        this.loadStatistics();
      },
      fail: () => {
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        });
      }
    });
  },

  // 加载统计数据
  loadStatistics() {
    wx.cloud.callFunction({
      name: 'authFunctions',
      data: {
        type: 'getUserStatistics'
      }
    }).then(res => {
      if (res.result.success) {
        this.setData({
          statistics: res.result.statistics
        });
      }
    }).catch(err => {
      console.error('加载统计数据失败:', err);
    });
  },

  // 联系客服
  onContactService() {
    wx.showActionSheet({
      itemList: ['复制客服QQ', '复制QQ群号'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 复制客服QQ
          wx.setClipboardData({
            data: '1922759464',
            success: () => {
              wx.showToast({
                title: '客服QQ已复制',
                icon: 'success'
              });
            }
          });
        } else if (res.tapIndex === 1) {
          // 复制QQ群号
          wx.setClipboardData({
            data: '967758553',
            success: () => {
              wx.showToast({
                title: 'QQ群号已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  },

  // 查看使用教程
  onViewTutorial() {
    wx.showModal({
      title: '使用教程',
      content: '1. 下载并安装XIAOFU工具箱\n2. 获取机器码（个人版需要）\n3. 在小程序中购买授权码\n4. 将授权码输入到软件中激活\n5. 开始使用工具箱功能',
      showCancel: false
    });
  },

  // 查看常见问题
  onViewFAQ() {
    const faqContent = `Q: 个人版和通用版有什么区别？
A: 个人版绑定机器码，只能在指定电脑使用；通用版不限制设备。

Q: 授权码过期了怎么办？
A: 需要重新购买新的授权码。

Q: 可以退款吗？
A: 授权码一经生成不支持退款，请谨慎购买。

Q: 更换电脑后个人版授权码还能用吗？
A: 不能，个人版绑定机器码，更换电脑需重新购买。`;

    wx.showModal({
      title: '常见问题',
      content: faqContent,
      showCancel: false
    });
  },

  // 关于我们
  onAbout() {
    wx.showModal({
      title: '关于XIAOFU工具箱',
      content: 'XIAOFU工具箱是一款功能强大的实用工具集合，为用户提供各种便捷的功能。\n\n作者: XIAOFU\nQQ: 1922759464\nQ群: 967758553',
      showCancel: false
    });
  },

  // 跳转到订单页面
  onGoToOrders() {
    wx.switchTab({
      url: '/pages/orders/index'
    });
  },

  // 跳转到购买页面
  onGoToPurchase() {
    wx.switchTab({
      url: '/pages/purchase/index'
    });
  }
});
