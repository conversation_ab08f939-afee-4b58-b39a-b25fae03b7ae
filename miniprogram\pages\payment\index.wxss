/* pages/payment/index.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid #1AAD19;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
  margin-top: 20rpx;
}

/* 支付内容 */
.payment-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 通用区域样式 */
.order-section,
.payment-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

/* 订单信息 */
.order-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-item:last-child {
  border-bottom: none;
}

.order-item.total {
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
  margin-top: 10rpx;
}

.order-item .label {
  font-size: 28rpx;
  color: #666;
}

.order-item .value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.order-item .value.price {
  font-size: 36rpx;
  color: #ff4444;
  font-weight: bold;
}

/* 支付方式 */
.payment-methods {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.payment-method {
  display: flex;
  align-items: center;
  padding: 25rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  transition: all 0.3s;
}

.payment-method.selected {
  border-color: #1AAD19;
  background-color: #f0f9f0;
}

.method-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.method-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.method-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.method-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.method-check {
  width: 40rpx;
  height: 40rpx;
}

.check-icon {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  position: relative;
}

.check-icon.checked {
  border-color: #1AAD19;
  background-color: #1AAD19;
}

.check-icon.checked::after {
  content: '';
  position: absolute;
  left: 12rpx;
  top: 6rpx;
  width: 12rpx;
  height: 20rpx;
  border: 3rpx solid white;
  border-top: none;
  border-left: none;
  transform: rotate(45deg);
}

/* 支付协议 */
.agreement-section {
  padding: 20rpx 30rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  text-align: center;
}

/* 支付操作 */
.payment-actions {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.pay-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #1AAD19, #0d8c13);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.pay-btn:active {
  opacity: 0.8;
}

.action-links {
  display: flex;
  justify-content: center;
  gap: 40rpx;
}

.link {
  font-size: 26rpx;
  color: #666;
  text-decoration: underline;
}
