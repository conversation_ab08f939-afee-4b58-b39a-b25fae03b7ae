<!--index.wxml-->
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 头部横幅 -->
    <view class="hero-section">
      <view class="hero-content">
        <text class="hero-title">XIAOFU工具箱</text>
        <text class="hero-subtitle">专业的授权管理平台</text>
        <text class="hero-description">安全可靠的授权码生成与管理服务</text>
        <view class="hero-buttons">
          <button class="hero-btn primary" bindtap="onPurchaseNow">立即购买</button>
          <button class="hero-btn secondary" bindtap="onContactService">联系客服</button>
        </view>
      </view>
      <view class="hero-image">🛡️</view>
    </view>

    <!-- 功能特色 -->
    <view class="features-section">
      <text class="section-title">产品特色</text>
      <view class="features-grid">
        <view class="feature-item" wx:for="{{features}}" wx:key="title">
          <view class="feature-icon" style="background-color: {{item.color}}">{{item.icon}}</view>
          <text class="feature-title">{{item.title}}</text>
          <text class="feature-desc">{{item.description}}</text>
        </view>
      </view>
    </view>

    <!-- 价格套餐 -->
    <view class="pricing-section">
      <text class="section-title">价格套餐</text>
      <view class="pricing-grid">
        <view
          class="price-card {{item.popular ? 'popular' : ''}}"
          wx:for="{{pricePackages}}"
          wx:key="name"
          bindtap="onSelectPackage"
          data-days="{{item.days}}"
        >
          <view class="popular-badge" wx:if="{{item.popular}}">推荐</view>
          <text class="package-name">{{item.name}}</text>
          <view class="package-price">
            <text class="current-price">¥{{item.price}}</text>
            <text class="original-price">¥{{item.originalPrice}}</text>
          </view>
          <text class="package-duration">{{item.days}}天使用期</text>
          <view class="package-features">
            <text class="feature-item" wx:for="{{item.features}}" wx:for-item="feature" wx:key="*this">
              ✓ {{feature}}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 数据统计 -->
    <view class="stats-section">
      <text class="section-title">用户信赖</text>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">{{statistics.totalUsers}}</text>
          <text class="stat-label">累计用户</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{statistics.totalOrders}}</text>
          <text class="stat-label">成功订单</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{statistics.satisfaction}}</text>
          <text class="stat-label">满意度</text>
        </view>
      </view>
    </view>

    <!-- 底部操作 -->
    <view class="bottom-actions">
      <button class="action-btn primary" bindtap="onPurchaseNow">立即购买授权码</button>
      <button class="action-btn secondary" bindtap="onViewOrders">查看我的订单</button>
    </view>
  </view>
</scroll-view>