<!--pages/paymentSuccess/index.wxml-->
<view class="container">
  <!-- 成功图标 -->
  <view class="success-header">
    <view class="success-icon">✅</view>
    <text class="success-title">支付成功</text>
    <text class="success-subtitle">授权码已生成，请妥善保管</text>
  </view>

  <!-- 订单信息 -->
  <view class="order-section" wx:if="{{orderInfo}}">
    <view class="section-title">订单信息</view>
    <view class="order-info">
      <view class="info-item">
        <text class="label">订单号</text>
        <text class="value">{{orderInfo.orderNumber}}</text>
      </view>
      <view class="info-item">
        <text class="label">商品名称</text>
        <text class="value">{{orderInfo.productName}}</text>
      </view>
      <view class="info-item">
        <text class="label">支付金额</text>
        <text class="value price">¥{{orderInfo.price}}</text>
      </view>
      <view class="info-item">
        <text class="label">授权时长</text>
        <text class="value">{{orderInfo.duration}}天</text>
      </view>
    </view>
  </view>

  <!-- 授权码区域 -->
  <view class="auth-code-section">
    <view class="section-title">
      您的授权码
      <text class="tip-text">（点击显示）</text>
    </view>
    
    <view class="auth-code-container" wx:if="{{!showAuthCode}}">
      <view class="auth-code-placeholder" bindtap="onShowAuthCode">
        <text class="placeholder-text">点击显示授权码</text>
        <text class="placeholder-icon">👁️</text>
      </view>
    </view>

    <view class="auth-code-container" wx:else>
      <view class="auth-code-content">
        <text class="auth-code-text">{{authCode}}</text>
        <button class="copy-btn" bindtap="onCopyAuthCode">复制</button>
      </view>
    </view>

    <view class="auth-code-tips">
      <text class="tip-item">• 授权码请妥善保管，丢失不补</text>
      <text class="tip-item">• 授权码仅限本次购买使用</text>
      <text class="tip-item">• 如有问题请及时联系客服</text>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="instructions-section">
    <view class="section-title">使用说明</view>
    <view class="instruction-steps">
      <view class="step-item">
        <view class="step-number">1</view>
        <text class="step-text">复制上方授权码</text>
      </view>
      <view class="step-item">
        <view class="step-number">2</view>
        <text class="step-text">打开XIAOFU工具箱软件</text>
      </view>
      <view class="step-item">
        <view class="step-number">3</view>
        <text class="step-text">在授权界面粘贴授权码</text>
      </view>
      <view class="step-item">
        <view class="step-number">4</view>
        <text class="step-text">点击激活开始使用</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions-section">
    <button class="action-btn primary" bindtap="onCopyAuthCode">
      复制授权码
    </button>
    <button class="action-btn secondary" bindtap="onViewOrders">
      查看我的订单
    </button>
    <button class="action-btn secondary" bindtap="onContinueShopping">
      继续购买
    </button>
  </view>

  <!-- 帮助链接 -->
  <view class="help-section">
    <text class="help-link" bindtap="onViewInstructions">详细使用说明</text>
    <text class="help-link" bindtap="onContactService">联系客服</text>
  </view>
</view>
