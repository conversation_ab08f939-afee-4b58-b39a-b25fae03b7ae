<!--pages/profile/index.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view class="user-info" wx:if="{{hasUserInfo}}">
      <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      <view class="user-details">
        <text class="nickname">{{userInfo.nickName}}</text>
        <text class="user-id">用户ID: {{userInfo.openId || '未获取'}}</text>
      </view>
    </view>
    
    <view class="login-section" wx:else>
      <view class="login-avatar">👤</view>
      <view class="login-content">
        <text class="login-title">登录后查看更多功能</text>
        <button class="login-btn" bindtap="onLogin">立即登录</button>
      </view>
    </view>
  </view>

  <!-- 统计数据 -->
  <view class="statistics-section" wx:if="{{hasUserInfo}}">
    <view class="stat-item">
      <text class="stat-number">{{statistics.totalOrders}}</text>
      <text class="stat-label">总订单数</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{statistics.activeAuthCodes}}</text>
      <text class="stat-label">有效授权码</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">¥{{statistics.totalSpent}}</text>
      <text class="stat-label">累计消费</text>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions" wx:if="{{hasUserInfo}}">
    <view class="action-item" bindtap="onGoToPurchase">
      <view class="action-icon">🛒</view>
      <text class="action-text">购买授权</text>
      <text class="action-arrow">></text>
    </view>
    <view class="action-item" bindtap="onGoToOrders">
      <view class="action-icon">📋</view>
      <text class="action-text">我的订单</text>
      <text class="action-arrow">></text>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group">
      <text class="menu-group-title">帮助与支持</text>
      <view class="menu-item" bindtap="onContactService">
        <view class="menu-icon">💬</view>
        <text class="menu-text">联系客服</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item" bindtap="onViewTutorial">
        <view class="menu-icon">📖</view>
        <text class="menu-text">使用教程</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item" bindtap="onViewFAQ">
        <view class="menu-icon">❓</view>
        <text class="menu-text">常见问题</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <view class="menu-group">
      <text class="menu-group-title">关于</text>
      <view class="menu-item" bindtap="onAbout">
        <view class="menu-icon">ℹ️</view>
        <text class="menu-text">关于我们</text>
        <text class="menu-arrow">></text>
      </view>
    </view>
  </view>

  <!-- 版权信息 -->
  <view class="footer">
    <text class="footer-text">XIAOFU工具箱 v2.0</text>
    <text class="footer-text">作者: XIAOFU | QQ: 1922759464</text>
  </view>
</view>
