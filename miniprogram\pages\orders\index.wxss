/* pages/orders/index.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid #1AAD19;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
  margin-top: 20rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.empty-btn {
  width: 200rpx;
  height: 70rpx;
  background-color: #1AAD19;
  color: white;
  font-size: 28rpx;
  border-radius: 35rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 订单列表 */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.order-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-info {
  flex: 1;
}

.order-number {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-paid {
  background-color: #d4edda;
  color: #155724;
}

.status-completed {
  background-color: #d1ecf1;
  color: #0c5460;
}

.status-cancelled {
  background-color: #f8d7da;
  color: #721c24;
}

.status-expired {
  background-color: #e2e3e5;
  color: #383d41;
}

/* 产品信息 */
.product-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.product-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.product-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.product-duration {
  font-size: 24rpx;
  color: #999;
}

.product-price {
  text-align: right;
}

.price {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff4444;
}

/* 授权码区域 */
.auth-code-section {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.auth-code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.auth-code-label {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.expire-time {
  font-size: 24rpx;
  color: #666;
}

.auth-code-content {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 8rpx;
  padding: 15rpx;
}

.auth-code-text {
  flex: 1;
  font-size: 24rpx;
  color: #333;
  word-break: break-all;
  line-height: 1.4;
  margin-right: 15rpx;
}

.copy-btn {
  width: 100rpx;
  height: 60rpx;
  background-color: #1AAD19;
  color: white;
  font-size: 24rpx;
  border-radius: 8rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 操作按钮 */
.order-actions {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
}

.action-btn {
  height: 60rpx;
  padding: 0 30rpx;
  font-size: 26rpx;
  border-radius: 30rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.primary {
  background-color: #1AAD19;
  color: white;
}

.action-btn.secondary {
  background-color: #f8f9fa;
  color: #666;
  border: 1rpx solid #e5e5e5;
}

.action-btn:active {
  opacity: 0.8;
}
