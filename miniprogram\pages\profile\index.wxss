/* pages/profile/index.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 用户信息区域 */
.user-section {
  background: linear-gradient(135deg, #1AAD19, #0d8c13);
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  color: white;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  border: 4rpx solid rgba(255,255,255,0.3);
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.nickname {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.user-id {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 登录区域 */
.login-section {
  display: flex;
  align-items: center;
}

.login-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background-color: rgba(255,255,255,0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  margin-right: 30rpx;
}

.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.login-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.login-btn {
  width: 160rpx;
  height: 60rpx;
  background-color: rgba(255,255,255,0.2);
  color: white;
  font-size: 28rpx;
  border-radius: 30rpx;
  border: 2rpx solid rgba(255,255,255,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 统计数据 */
.statistics-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #1AAD19;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 快捷操作 */
.quick-actions {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.action-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.action-item:last-child {
  border-bottom: none;
}

.action-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.action-text {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.action-arrow {
  font-size: 32rpx;
  color: #ccc;
}

/* 功能菜单 */
.menu-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.menu-group {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.menu-group-title {
  display: block;
  padding: 20rpx 30rpx 10rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
  background-color: #f8f9fa;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.menu-arrow {
  font-size: 28rpx;
  color: #ccc;
}

/* 版权信息 */
.footer {
  text-align: center;
  padding: 40rpx 20rpx;
  margin-top: 40rpx;
}

.footer-text {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.footer-text:last-child {
  margin-bottom: 0;
}
