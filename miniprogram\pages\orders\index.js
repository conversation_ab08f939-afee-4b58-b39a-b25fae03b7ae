// pages/orders/index.js
Page({
  data: {
    orders: [],
    loading: true,
    isEmpty: false
  },

  onLoad() {
    this.loadOrders();
  },

  onShow() {
    // 每次显示页面时刷新订单列表
    this.loadOrders();
  },

  onPullDownRefresh() {
    this.loadOrders().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载订单列表
  loadOrders() {
    this.setData({ loading: true });
    
    return wx.cloud.callFunction({
      name: 'authFunctions',
      data: {
        type: 'getOrders'
      }
    }).then(res => {
      if (res.result.success) {
        this.setData({
          orders: res.result.orders || [],
          isEmpty: !res.result.orders || res.result.orders.length === 0,
          loading: false
        });
      } else {
        this.setData({
          orders: [],
          isEmpty: true,
          loading: false
        });
        wx.showToast({
          title: res.result.message || '加载失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('加载订单失败:', err);
      this.setData({
        orders: [],
        isEmpty: true,
        loading: false
      });
      wx.showToast({
        title: '加载订单失败',
        icon: 'none'
      });
    });
  },

  // 复制授权码
  onCopyAuthCode(e) {
    const authCode = e.currentTarget.dataset.code;
    if (!authCode) {
      wx.showToast({
        title: '授权码不存在',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: authCode,
      success: () => {
        wx.showToast({
          title: '授权码已复制',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 查看订单详情
  onViewOrderDetail(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/orderDetail/index?orderId=${orderId}`
    });
  },

  // 继续支付
  onContinuePay(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/payment/index?orderId=${orderId}`
    });
  },

  // 联系客服
  onContactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服QQ: 1922759464\nQ群: 967758553\n\n请复制QQ号添加客服好友',
      confirmText: '复制QQ号',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: '1922759464',
            success: () => {
              wx.showToast({
                title: 'QQ号已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '待支付',
      'paid': '已支付',
      'completed': '已完成',
      'cancelled': '已取消',
      'expired': '已过期'
    };
    return statusMap[status] || '未知状态';
  },

  // 获取状态样式类
  getStatusClass(status) {
    const classMap = {
      'pending': 'status-pending',
      'paid': 'status-paid',
      'completed': 'status-completed',
      'cancelled': 'status-cancelled',
      'expired': 'status-expired'
    };
    return classMap[status] || 'status-unknown';
  }
});
