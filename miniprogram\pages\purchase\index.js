// pages/purchase/index.js
Page({
  data: {
    authTypes: [
      {
        id: 'personal',
        name: '个人版',
        description: '绑定机器码，仅限指定电脑使用',
        icon: '🔒',
        needMachineCode: true
      },
      {
        id: 'universal',
        name: '通用版',
        description: '不限制电脑，任意设备可用',
        icon: '🌐',
        needMachineCode: false
      }
    ],
    durations: [
      { days: 7, name: '7天', price: 9.9, originalPrice: 19.9 },
      { days: 30, name: '30天', price: 29.9, originalPrice: 59.9 },
      { days: 90, name: '90天', price: 79.9, originalPrice: 159.9 },
      { days: 365, name: '365天', price: 199.9, originalPrice: 399.9 }
    ],
    selectedAuthType: 'personal',
    selectedDuration: 30,
    machineCode: '',
    showMachineCodeTip: false,
    userInfo: null,
    selectedPrice: 29.9,
    selectedDurationName: '30天'
  },

  onLoad(options) {
    this.getUserInfo();
    // 如果从首页传来了选中的时长，设置默认值
    if (options.selectedDuration) {
      const days = parseInt(options.selectedDuration);
      const durationInfo = this.data.durations.find(d => d.days === days);
      this.setData({
        selectedDuration: days,
        selectedPrice: durationInfo ? durationInfo.price : 29.9,
        selectedDurationName: durationInfo ? durationInfo.name : '30天'
      });
    }
  },

  // 获取用户信息
  getUserInfo() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        this.setData({
          userInfo: res.userInfo
        });
      },
      fail: () => {
        console.log('用户拒绝授权');
      }
    });
  },

  // 选择授权类型
  onSelectAuthType(e) {
    const authType = e.currentTarget.dataset.type;
    this.setData({
      selectedAuthType: authType,
      machineCode: authType === 'universal' ? '' : this.data.machineCode
    });
  },

  // 选择时长
  onSelectDuration(e) {
    const days = e.currentTarget.dataset.days;
    const durationInfo = this.data.durations.find(d => d.days === days);
    this.setData({
      selectedDuration: days,
      selectedPrice: durationInfo ? durationInfo.price : 0,
      selectedDurationName: durationInfo ? durationInfo.name : ''
    });
  },

  // 输入机器码
  onMachineCodeInput(e) {
    this.setData({
      machineCode: e.detail.value
    });
  },

  // 显示机器码获取提示
  onShowMachineCodeTip() {
    this.setData({
      showMachineCodeTip: true
    });
  },

  // 关闭机器码提示
  onCloseMachineCodeTip() {
    this.setData({
      showMachineCodeTip: false
    });
  },

  // 立即购买
  onPurchase() {
    const { selectedAuthType, selectedDuration, machineCode, durations } = this.data;
    
    // 验证机器码
    if (selectedAuthType === 'personal' && !machineCode.trim()) {
      wx.showToast({
        title: '请输入机器码',
        icon: 'none'
      });
      return;
    }

    // 获取价格信息
    const durationInfo = durations.find(d => d.days === selectedDuration);
    if (!durationInfo) {
      wx.showToast({
        title: '请选择授权时长',
        icon: 'none'
      });
      return;
    }

    // 创建订单
    this.createOrder({
      authType: selectedAuthType,
      duration: selectedDuration,
      machineCode: selectedAuthType === 'personal' ? machineCode.trim() : '',
      price: durationInfo.price,
      productName: `${selectedAuthType === 'personal' ? '个人版' : '通用版'} ${durationInfo.name}`
    });
  },

  // 创建订单
  createOrder(orderData) {
    wx.showLoading({
      title: '创建订单中...'
    });

    wx.cloud.callFunction({
      name: 'authFunctions',
      data: {
        type: 'createOrder',
        orderData: orderData
      }
    }).then(res => {
      wx.hideLoading();
      if (res.result.success) {
        // 跳转到支付页面
        wx.navigateTo({
          url: `/pages/payment/index?orderId=${res.result.orderId}`
        });
      } else {
        wx.showToast({
          title: res.result.message || '创建订单失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('创建订单失败:', err);
      wx.showToast({
        title: '创建订单失败',
        icon: 'none'
      });
    });
  },

  // 查看购买须知
  onShowNotice() {
    wx.showModal({
      title: '购买须知',
      content: '1. 个人版授权码绑定机器码，仅限指定电脑使用\n2. 通用版授权码不限制设备，可在任意电脑使用\n3. 授权码一经生成不可退款\n4. 如有问题请联系客服QQ: 1922759464',
      showCancel: false
    });
  }
});
