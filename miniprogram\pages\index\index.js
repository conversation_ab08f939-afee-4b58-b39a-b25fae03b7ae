// index.js
Page({
  data: {
    features: [
      {
        icon: '🔒',
        title: '个人版授权',
        description: '绑定机器码，专属设备使用',
        color: '#FF6B6B'
      },
      {
        icon: '🌐',
        title: '通用版授权',
        description: '不限设备，随时随地使用',
        color: '#4ECDC4'
      },
      {
        icon: '⚡',
        title: '即时生成',
        description: '支付成功立即获得授权码',
        color: '#45B7D1'
      },
      {
        icon: '🛡️',
        title: '安全可靠',
        description: '多重加密保障授权安全',
        color: '#96CEB4'
      }
    ],
    pricePackages: [
      {
        name: '体验版',
        days: 7,
        price: 9.9,
        originalPrice: 19.9,
        features: ['基础功能', '7天使用期', '客服支持'],
        popular: false
      },
      {
        name: '标准版',
        days: 30,
        price: 29.9,
        originalPrice: 59.9,
        features: ['全部功能', '30天使用期', '优先客服', '使用教程'],
        popular: true
      },
      {
        name: '专业版',
        days: 90,
        price: 79.9,
        originalPrice: 159.9,
        features: ['全部功能', '90天使用期', '专属客服', '定制功能'],
        popular: false
      }
    ],
    statistics: {
      totalUsers: '10000+',
      totalOrders: '50000+',
      satisfaction: '99.8%'
    }
  },
  onLoad() {
    // 页面加载时的初始化
  },

  // 立即购买
  onPurchaseNow() {
    wx.switchTab({
      url: '/pages/purchase/index'
    });
  },

  // 查看订单
  onViewOrders() {
    wx.switchTab({
      url: '/pages/orders/index'
    });
  },

  // 联系客服
  onContactService() {
    wx.showActionSheet({
      itemList: ['复制客服QQ', '复制QQ群号'],
      success: (res) => {
        if (res.tapIndex === 0) {
          wx.setClipboardData({
            data: '1922759464',
            success: () => {
              wx.showToast({
                title: '客服QQ已复制',
                icon: 'success'
              });
            }
          });
        } else if (res.tapIndex === 1) {
          wx.setClipboardData({
            data: '967758553',
            success: () => {
              wx.showToast({
                title: 'QQ群号已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  },

  // 选择套餐
  onSelectPackage(e) {
    const days = e.currentTarget.dataset.days;
    wx.navigateTo({
      url: `/pages/purchase/index?selectedDuration=${days}`
    });
  },
});
