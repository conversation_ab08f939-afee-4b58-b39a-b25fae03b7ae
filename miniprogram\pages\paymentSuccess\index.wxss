/* pages/paymentSuccess/index.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 成功头部 */
.success-header {
  text-align: center;
  padding: 60rpx 20rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.success-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.success-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #1AAD19;
  margin-bottom: 12rpx;
}

.success-subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 通用区域样式 */
.order-section,
.auth-code-section,
.instructions-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}

/* 订单信息 */
.order-info {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item .label {
  font-size: 28rpx;
  color: #666;
}

.info-item .value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.info-item .value.price {
  color: #ff4444;
  font-weight: bold;
}

/* 授权码区域 */
.auth-code-container {
  margin-bottom: 20rpx;
}

.auth-code-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background: #f8f9fa;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  cursor: pointer;
}

.placeholder-text {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.placeholder-icon {
  font-size: 32rpx;
}

.auth-code-content {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
}

.auth-code-text {
  flex: 1;
  font-size: 24rpx;
  color: #333;
  word-break: break-all;
  line-height: 1.4;
  margin-right: 15rpx;
  font-family: monospace;
}

.copy-btn {
  width: 120rpx;
  height: 60rpx;
  background-color: #1AAD19;
  color: white;
  font-size: 24rpx;
  border-radius: 8rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-code-tips {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tip-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 使用说明 */
.instruction-steps {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.step-item {
  display: flex;
  align-items: center;
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  background: #1AAD19;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.step-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

/* 操作按钮 */
.actions-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.action-btn {
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.primary {
  background: linear-gradient(135deg, #1AAD19, #0d8c13);
  color: white;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e5e5e5;
}

.action-btn:active {
  opacity: 0.8;
}

/* 帮助链接 */
.help-section {
  display: flex;
  justify-content: center;
  gap: 40rpx;
  padding: 20rpx;
}

.help-link {
  font-size: 26rpx;
  color: #1AAD19;
  text-decoration: underline;
}
