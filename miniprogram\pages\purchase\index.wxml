<!--pages/purchase/index.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">购买授权码</text>
    <text class="page-subtitle">选择适合您的授权方案</text>
  </view>

  <!-- 授权类型选择 -->
  <view class="section">
    <view class="section-title">授权类型</view>
    <view class="auth-types">
      <view 
        class="auth-type-item {{selectedAuthType === item.id ? 'selected' : ''}}"
        wx:for="{{authTypes}}" 
        wx:key="id"
        bindtap="onSelectAuthType"
        data-type="{{item.id}}"
      >
        <view class="auth-type-icon">{{item.icon}}</view>
        <view class="auth-type-content">
          <view class="auth-type-name">{{item.name}}</view>
          <view class="auth-type-desc">{{item.description}}</view>
        </view>
        <view class="auth-type-check">
          <view class="check-icon {{selectedAuthType === item.id ? 'checked' : ''}}"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 机器码输入 (仅个人版显示) -->
  <view class="section" wx:if="{{selectedAuthType === 'personal'}}">
    <view class="section-title">
      机器码
      <text class="tip-btn" bindtap="onShowMachineCodeTip">如何获取?</text>
    </view>
    <view class="machine-code-input">
      <textarea 
        class="machine-code-textarea"
        placeholder="请输入您的机器码"
        value="{{machineCode}}"
        bindinput="onMachineCodeInput"
        maxlength="500"
      ></textarea>
    </view>
  </view>

  <!-- 授权时长选择 -->
  <view class="section">
    <view class="section-title">授权时长</view>
    <view class="durations">
      <view 
        class="duration-item {{selectedDuration === item.days ? 'selected' : ''}}"
        wx:for="{{durations}}" 
        wx:key="days"
        bindtap="onSelectDuration"
        data-days="{{item.days}}"
      >
        <view class="duration-name">{{item.name}}</view>
        <view class="duration-price">
          <text class="current-price">¥{{item.price}}</text>
          <text class="original-price">¥{{item.originalPrice}}</text>
        </view>
        <view class="duration-check">
          <view class="check-icon {{selectedDuration === item.days ? 'checked' : ''}}"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 购买按钮 -->
  <view class="purchase-section">
    <view class="price-info">
      <text class="total-price">
        ¥{{selectedPrice}}
      </text>
      <text class="price-desc">
        {{selectedAuthType === 'personal' ? '个人版' : '通用版'}}
        {{selectedDurationName}}
      </text>
    </view>
    <button class="purchase-btn" bindtap="onPurchase">立即购买</button>
  </view>

  <!-- 购买须知 -->
  <view class="notice-section">
    <text class="notice-link" bindtap="onShowNotice">购买须知</text>
  </view>
</view>

<!-- 机器码获取提示弹窗 -->
<view class="modal-mask" wx:if="{{showMachineCodeTip}}" bindtap="onCloseMachineCodeTip">
  <view class="modal-content" catchtap="">
    <view class="modal-header">
      <text class="modal-title">如何获取机器码</text>
      <text class="modal-close" bindtap="onCloseMachineCodeTip">×</text>
    </view>
    <view class="modal-body">
      <view class="tip-item">
        <text class="tip-number">1.</text>
        <text class="tip-text">打开XIAOFU工具箱软件</text>
      </view>
      <view class="tip-item">
        <text class="tip-number">2.</text>
        <text class="tip-text">点击"获取机器码"按钮</text>
      </view>
      <view class="tip-item">
        <text class="tip-number">3.</text>
        <text class="tip-text">复制显示的机器码到此处</text>
      </view>
      <view class="tip-note">
        注意：机器码与您的电脑硬件绑定，更换电脑需重新购买
      </view>
    </view>
  </view>
</view>
