/**index.wxss**/
.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 头部横幅 */
.hero-section {
  background: linear-gradient(135deg, #1AAD19, #0d8c13);
  padding: 60rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
}

.hero-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.hero-title {
  font-size: 56rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.hero-subtitle {
  font-size: 32rpx;
  margin-bottom: 12rpx;
  opacity: 0.9;
}

.hero-description {
  font-size: 28rpx;
  opacity: 0.8;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.hero-buttons {
  display: flex;
  gap: 20rpx;
}

.hero-btn {
  height: 72rpx;
  padding: 0 32rpx;
  border-radius: 36rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-btn.primary {
  background-color: white;
  color: #1AAD19;
}

.hero-btn.secondary {
  background-color: transparent;
  color: white;
  border: 2rpx solid rgba(255,255,255,0.5);
}

.hero-image {
  font-size: 120rpx;
  margin-left: 40rpx;
}

/* 功能特色 */
.features-section {
  padding: 60rpx 40rpx;
  background: white;
}

.section-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40rpx 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.feature-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-bottom: 20rpx;
  color: white;
}

.feature-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 价格套餐 */
.pricing-section {
  padding: 60rpx 40rpx;
  background: #f8f9fa;
}

.pricing-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.price-card {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  position: relative;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  transition: all 0.3s;
}

.price-card.popular {
  border: 3rpx solid #1AAD19;
  transform: scale(1.02);
}

.popular-badge {
  position: absolute;
  top: -10rpx;
  right: 30rpx;
  background: #1AAD19;
  color: white;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.package-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.package-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 16rpx;
}

.current-price {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff4444;
}

.original-price {
  font-size: 28rpx;
  color: #999;
  text-decoration: line-through;
  margin-left: 16rpx;
}

.package-duration {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.package-features {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.package-features .feature-item {
  font-size: 26rpx;
  color: #333;
  text-align: left;
  padding: 0;
  background: none;
  border-radius: 0;
  display: block;
}

/* 数据统计 */
.stats-section {
  padding: 60rpx 40rpx;
  background: white;
}

.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #1AAD19;
  margin-bottom: 12rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

/* 底部操作 */
.bottom-actions {
  padding: 40rpx;
  background: white;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-btn {
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.primary {
  background: linear-gradient(135deg, #1AAD19, #0d8c13);
  color: white;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e5e5e5;
}
