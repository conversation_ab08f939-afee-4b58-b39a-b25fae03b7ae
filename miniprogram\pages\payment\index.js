// pages/payment/index.js
Page({
  data: {
    orderId: '',
    orderInfo: null,
    loading: true,
    paymentMethods: [
      {
        id: 'wechat',
        name: '微信支付',
        icon: '💳',
        description: '使用微信余额或绑定银行卡支付',
        enabled: true
      }
    ],
    selectedPaymentMethod: 'wechat'
  },

  onLoad(options) {
    if (options.orderId) {
      this.setData({
        orderId: options.orderId
      });
      this.loadOrderInfo();
    } else {
      wx.showToast({
        title: '订单信息错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载订单信息
  loadOrderInfo() {
    wx.showLoading({
      title: '加载中...'
    });

    wx.cloud.callFunction({
      name: 'authFunctions',
      data: {
        type: 'getOrderInfo',
        orderId: this.data.orderId
      }
    }).then(res => {
      wx.hideLoading();
      if (res.result.success) {
        this.setData({
          orderInfo: res.result.orderInfo,
          loading: false
        });
      } else {
        wx.showToast({
          title: res.result.message || '加载订单失败',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('加载订单失败:', err);
      wx.showToast({
        title: '加载订单失败',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    });
  },

  // 选择支付方式
  onSelectPaymentMethod(e) {
    const methodId = e.currentTarget.dataset.id;
    this.setData({
      selectedPaymentMethod: methodId
    });
  },

  // 立即支付
  onPay() {
    const { orderInfo, selectedPaymentMethod } = this.data;
    
    if (!orderInfo) {
      wx.showToast({
        title: '订单信息错误',
        icon: 'none'
      });
      return;
    }

    // 模拟支付流程（实际项目中需要集成真实的微信支付）
    wx.showModal({
      title: '支付确认',
      content: `确认支付 ¥${orderInfo.price} 购买 ${orderInfo.productName}？`,
      success: (res) => {
        if (res.confirm) {
          this.processPayment();
        }
      }
    });
  },

  // 处理支付
  processPayment() {
    wx.showLoading({
      title: '支付中...'
    });

    // 模拟支付成功（实际项目中这里应该调用微信支付API）
    setTimeout(() => {
      this.mockPaymentSuccess();
    }, 2000);
  },

  // 模拟支付成功
  mockPaymentSuccess() {
    // 更新订单状态为已支付
    wx.cloud.callFunction({
      name: 'authFunctions',
      data: {
        type: 'updateOrderStatus',
        orderId: this.data.orderId,
        status: 'paid'
      }
    }).then(res => {
      if (res.result.success) {
        // 生成授权码
        return wx.cloud.callFunction({
          name: 'authFunctions',
          data: {
            type: 'generateAuthCode',
            orderId: this.data.orderId
          }
        });
      } else {
        throw new Error(res.result.message || '更新订单状态失败');
      }
    }).then(res => {
      wx.hideLoading();
      if (res.result.success) {
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        });
        
        // 跳转到支付成功页面
        wx.redirectTo({
          url: `/pages/paymentSuccess/index?orderId=${this.data.orderId}&authCode=${res.result.authCode}`
        });
      } else {
        wx.showToast({
          title: res.result.message || '生成授权码失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('支付处理失败:', err);
      wx.showToast({
        title: '支付失败，请重试',
        icon: 'none'
      });
    });
  },

  // 取消支付
  onCancel() {
    wx.showModal({
      title: '取消支付',
      content: '确定要取消支付吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  // 联系客服
  onContactService() {
    wx.showActionSheet({
      itemList: ['复制客服QQ', '复制QQ群号'],
      success: (res) => {
        if (res.tapIndex === 0) {
          wx.setClipboardData({
            data: '1922759464',
            success: () => {
              wx.showToast({
                title: '客服QQ已复制',
                icon: 'success'
              });
            }
          });
        } else if (res.tapIndex === 1) {
          wx.setClipboardData({
            data: '967758553',
            success: () => {
              wx.showToast({
                title: 'QQ群号已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  }
});
